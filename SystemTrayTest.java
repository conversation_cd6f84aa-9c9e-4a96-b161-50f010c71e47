import javafx.application.Application;
import javafx.application.Platform;
import javafx.scene.Scene;
import javafx.scene.control.Button;
import javafx.scene.layout.VBox;
import javafx.stage.Stage;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;

/**
 * 简单的系统托盘测试程序
 * 用于验证托盘功能是否正常工作
 */
public class SystemTrayTest extends Application {
    
    private Stage primaryStage;
    private SystemTray systemTray;
    private TrayIcon trayIcon;
    
    @Override
    public void start(Stage primaryStage) {
        this.primaryStage = primaryStage;
        
        // 创建简单的UI
        Button minimizeButton = new Button("最小化到托盘");
        minimizeButton.setOnAction(e -> minimizeToTray());
        
        VBox root = new VBox(10);
        root.getChildren().add(minimizeButton);
        
        Scene scene = new Scene(root, 300, 200);
        primaryStage.setTitle("系统托盘测试");
        primaryStage.setScene(scene);
        primaryStage.show();
        
        // 设置关闭事件
        primaryStage.setOnCloseRequest(event -> {
            event.consume();
            minimizeToTray();
        });
    }
    
    private void minimizeToTray() {
        if (!SystemTray.isSupported()) {
            System.err.println("系统托盘不支持");
            return;
        }
        
        Platform.runLater(() -> primaryStage.hide());
        
        SwingUtilities.invokeLater(() -> {
            try {
                if (systemTray == null) {
                    systemTray = SystemTray.getSystemTray();
                }
                
                if (trayIcon == null) {
                    // 创建一个简单的图标
                    Image image = Toolkit.getDefaultToolkit().createImage(new byte[0]);
                    
                    PopupMenu popupMenu = new PopupMenu();
                    MenuItem showItem = new MenuItem("显示窗口");
                    MenuItem exitItem = new MenuItem("退出");
                    
                    showItem.addActionListener(e -> {
                        System.out.println("菜单项：显示窗口被点击");
                        Platform.runLater(this::showWindow);
                    });
                    
                    exitItem.addActionListener(e -> {
                        System.out.println("菜单项：退出被点击");
                        Platform.runLater(this::exitApp);
                    });
                    
                    popupMenu.add(showItem);
                    popupMenu.add(exitItem);
                    
                    trayIcon = new TrayIcon(image, "测试应用", popupMenu);
                    trayIcon.setImageAutoSize(true);
                    
                    // 添加双击事件
                    trayIcon.addMouseListener(new MouseAdapter() {
                        @Override
                        public void mouseClicked(MouseEvent e) {
                            System.out.println("托盘图标被点击，点击次数: " + e.getClickCount());
                            if (e.getClickCount() == 2) {
                                System.out.println("检测到双击事件");
                                Platform.runLater(() -> {
                                    System.out.println("在JavaFX线程中执行显示窗口");
                                    showWindow();
                                });
                            }
                        }
                    });
                    
                    systemTray.add(trayIcon);
                    System.out.println("托盘图标已添加");
                }
                
                trayIcon.displayMessage("应用已最小化", "双击托盘图标或右键菜单恢复", TrayIcon.MessageType.INFO);
                
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
    }
    
    private void showWindow() {
        System.out.println("正在显示窗口...");
        try {
            if (primaryStage != null) {
                primaryStage.setIconified(false);
                primaryStage.show();
                primaryStage.toFront();
                primaryStage.requestFocus();
                System.out.println("窗口已显示");
            }
        } catch (Exception e) {
            System.err.println("显示窗口时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private void exitApp() {
        System.out.println("正在退出应用...");
        try {
            if (trayIcon != null && systemTray != null) {
                systemTray.remove(trayIcon);
                System.out.println("托盘图标已移除");
            }
            Platform.exit();
            System.exit(0);
        } catch (Exception e) {
            System.err.println("退出应用时发生错误: " + e.getMessage());
            System.exit(1);
        }
    }
    
    public static void main(String[] args) {
        launch(args);
    }
}
