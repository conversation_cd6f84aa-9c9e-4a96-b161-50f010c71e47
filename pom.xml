<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.io661</groupId>
    <artifactId>extension</artifactId>
    <version>1.0-SNAPSHOT</version>
    <name>extension</name>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <junit.version>5.10.2</junit.version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>org.openjfx</groupId>
            <artifactId>javafx-controls</artifactId>
            <version>21</version>
        </dependency>
        <dependency>
            <groupId>org.openjfx</groupId>
            <artifactId>javafx-fxml</artifactId>
            <version>21</version>
        </dependency>
        <dependency>
            <groupId>org.openjfx</groupId>
            <artifactId>javafx-web</artifactId>
            <version>21</version>
        </dependency>
        <dependency>
            <groupId>org.controlsfx</groupId>
            <artifactId>controlsfx</artifactId>
            <version>11.2.1</version>
        </dependency>
        <dependency>
            <groupId>com.dlsc.formsfx</groupId>
            <artifactId>formsfx-core</artifactId>
            <version>11.6.0</version>
            <exclusions>
                <exclusion>
                    <groupId>org.openjfx</groupId>
                    <artifactId>*</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.kordamp.bootstrapfx</groupId>
            <artifactId>bootstrapfx-core</artifactId>
            <version>0.4.0</version>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-api</artifactId>
            <version>${junit.version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-engine</artifactId>
            <version>${junit.version}</version>
            <scope>test</scope>
        </dependency>
        <!--导入lombok-->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.36</version>
            <scope>provided</scope>
        </dependency>

        <!--导入sqlite-->
        <dependency>
            <groupId>org.xerial</groupId>
            <artifactId>sqlite-jdbc</artifactId>
            <version>3.43.0.0</version>
        </dependency>

        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <version>2.11.0</version>
        </dependency>

        <!--导入selenium-->
        <dependency>
            <groupId>org.seleniumhq.selenium</groupId>
            <artifactId>selenium-java</artifactId>
            <version>4.32.0</version> <!-- 或更高版本 -->
        </dependency>
        <dependency>
            <groupId>org.seleniumhq.selenium</groupId>
            <artifactId>selenium-devtools-v119</artifactId>
            <version>4.18.0</version>
        </dependency>
        <dependency>
            <groupId>io.github.bonigarcia</groupId>
            <artifactId>webdrivermanager</artifactId>
            <version>6.1.0</version>
        </dependency>


    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.13.0</version>
                <configuration>
                    <source>21</source>
                    <target>21</target>
                    <annotationProcessorPaths>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>1.18.30</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.openjfx</groupId>
                <artifactId>javafx-maven-plugin</artifactId>
                <version>0.0.8</version>
                <configuration>
                    <mainClass>com.io661.extension.IO661Extension</mainClass>
                    <launcher>io661</launcher>
                    <jlinkImageName>io661</jlinkImageName>
                    <jlinkZipName>io661</jlinkZipName>
                    <noHeaderFiles>true</noHeaderFiles>
                </configuration>
            </plugin>
            <!--打包exe-->
            <plugin>
                <groupId>io.github.fvarrui</groupId>
                <artifactId>javapackager</artifactId>
                <version>1.6.7</version>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>package</goal>
                        </goals>
                        <configuration>

                            <mainClass>com.io661.extension.Main</mainClass>

                            <!-- 打包配置 -->
                            <bundleJre>true</bundleJre>
                            <generateInstaller>false</generateInstaller>
                            <administratorRequired>false</administratorRequired>
                            <platform>windows</platform>

                            <name>io661</name> <!-- 应用名称（影响EXE文件名） -->

                            <!-- 图标配置 -->
                            <winConfig>
                                <icoFile>src/main/resources/com/io661/extension/img/icon.ico</icoFile>
                            </winConfig>

                            <!-- 添加JVM参数来处理模块化问题 -->
                            <vmArgs>
                                <vmArg>--add-modules</vmArg>
                                <vmArg>ALL-SYSTEM,ALL-MODULE-PATH</vmArg>
                                <vmArg>--add-opens</vmArg>
                                <vmArg>java.base/java.lang=ALL-UNNAMED</vmArg>
                                <vmArg>--add-opens</vmArg>
                                <vmArg>java.base/java.util=ALL-UNNAMED</vmArg>
                                <vmArg>--add-opens</vmArg>
                                <vmArg>java.base/java.net=ALL-UNNAMED</vmArg>
                                <vmArg>--add-opens</vmArg>
                                <vmArg>java.base/java.io=ALL-UNNAMED</vmArg>
                                <vmArg>--add-opens</vmArg>
                                <vmArg>javafx.graphics/javafx.scene=ALL-UNNAMED</vmArg>
                                <vmArg>--add-reads</vmArg>
                                <vmArg>com.io661.extension=ALL-UNNAMED</vmArg>
                                <vmArg>--add-exports</vmArg>
                                <vmArg>java.base/sun.nio.ch=ALL-UNNAMED</vmArg>
                                <vmArg>--add-exports</vmArg>
                                <vmArg>java.base/sun.security.util=ALL-UNNAMED</vmArg>
                                <vmArg>--illegal-access=permit</vmArg>
                            </vmArgs>

                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
    <!-- 下载镜像 -->
    <repositories>
        <repository>
            <id>huawei_cloud</id>
            <name>huawei</name>
            <url>https://mirrors.huaweicloud.com/repository/maven/</url>
        </repository>
        <repository>
            <id>aliyun_maven</id>
            <name>aliyun</name>
            <url>https://maven.aliyun.com/repository/public</url>
        </repository>
    </repositories>
</project>