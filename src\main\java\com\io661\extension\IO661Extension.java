package com.io661.extension;

import com.io661.extension.commonResult.CommonShowAlert;
import com.io661.extension.controller.LoginController;
import com.io661.extension.controller.MainWindowController;
import com.io661.extension.service.Impl.UserServiceImpl;
import com.io661.extension.service.UserService;
import com.io661.extension.util.SSLCertificateManager;
import com.io661.extension.util.User.UserCookieManager;
import javafx.application.Application;
import javafx.application.Platform;
import javafx.fxml.FXMLLoader;
import javafx.scene.Parent;
import javafx.scene.Scene;
import javafx.scene.control.Alert;
import javafx.scene.control.ButtonType;
import javafx.scene.effect.ColorAdjust;
import javafx.scene.image.Image;
import javafx.scene.image.ImageView;
import javafx.scene.layout.Region;
import javafx.stage.Stage;
import javafx.stage.StageStyle;
import lombok.Getter;

import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.io.IOException;
import java.net.URL;
import java.util.Objects;

public class IO661Extension extends Application {
    @Getter
    private static Stage mainStage;
    @Getter
    private static Stage loginStage;

    private TrayIcon trayIcon;
    private SystemTray systemTray;
    private static final String DEFAULT_ICON = "/com/io661/extension/img/Logo.png";
    private static final String ALERT_CSS = "/com/io661/extension/css/alert.css";
    @Getter
    private Stage primaryStage;

    public static void closeLoginStage() {
        if (loginStage != null && loginStage.isShowing()) {
            loginStage.close();
        }

        if (mainStage != null && mainStage.getScene() != null && mainStage.getScene().getRoot() != null) {
            mainStage.getScene().getRoot().setEffect(null);
            mainStage.getScene().getRoot().setDisable(false);
            mainStage.getScene().getRoot().setMouseTransparent(false);
        }

        System.out.println("登录窗口已关闭，主窗口状态已完全恢复");
    }

    public static void main(String[] args) {
        forceAllSSLProperties();
        forceInitializeSSL();
        System.setProperty("file.encoding", "UTF-8");
        launch(args);
    }

    private static void forceAllSSLProperties() {
        try {
            System.setProperty("javax.net.ssl.trustStore", "");
            System.setProperty("javax.net.ssl.trustStorePassword", "");
            System.setProperty("javax.net.ssl.keyStore", "");
            System.setProperty("javax.net.ssl.keyStorePassword", "");
            System.setProperty("com.sun.net.ssl.checkRevocation", "false");
            System.setProperty("com.sun.security.enableCRLDP", "false");
            System.setProperty("sun.security.ssl.allowUnsafeRenegotiation", "true");
            System.setProperty("sun.security.ssl.allowLegacyHelloMessages", "true");
            System.setProperty("jdk.tls.allowUnsafeServerCertChange", "true");
            System.setProperty("jdk.tls.allowUnsafeRenegotiation", "true");
            System.setProperty("jdk.tls.rejectClientInitiatedRenegotiation", "false");
            System.setProperty("https.protocols", "TLSv1,TLSv1.1,TLSv1.2,TLSv1.3");
            System.setProperty("jdk.tls.client.protocols", "TLSv1,TLSv1.1,TLSv1.2,TLSv1.3");
            System.setProperty("jdk.tls.server.protocols", "TLSv1,TLSv1.1,TLSv1.2,TLSv1.3");
            System.setProperty("jsse.enableSNIExtension", "false");
            System.out.println("✓ 强制SSL系统属性设置完成");
        } catch (Exception e) {
            System.err.println("✗ 设置SSL系统属性失败: " + e.getMessage());
        }
    }

    private static void forceInitializeSSL() {
        try {
            javax.net.ssl.TrustManager[] trustAllCerts = new javax.net.ssl.TrustManager[] {
                    new javax.net.ssl.X509TrustManager() {
                        public java.security.cert.X509Certificate[] getAcceptedIssuers() {
                            return new java.security.cert.X509Certificate[0];
                        }
                        public void checkClientTrusted(java.security.cert.X509Certificate[] certs, String authType) {}
                        public void checkServerTrusted(java.security.cert.X509Certificate[] certs, String authType) {}
                    }
            };

            String[] protocols = {"TLSv1.3", "TLSv1.2", "TLSv1.1", "TLS", "SSL"};
            javax.net.ssl.SSLContext sslContext = null;

            for (String protocol : protocols) {
                try {
                    sslContext = javax.net.ssl.SSLContext.getInstance(protocol);
                    sslContext.init(null, trustAllCerts, new java.security.SecureRandom());
                    javax.net.ssl.SSLContext.setDefault(sslContext);
                    javax.net.ssl.HttpsURLConnection.setDefaultSSLSocketFactory(sslContext.getSocketFactory());
                    javax.net.ssl.HttpsURLConnection.setDefaultHostnameVerifier((hostname, session) -> true);
                    System.out.println("✓ SSL上下文初始化成功，协议: " + protocol);
                    break;
                } catch (Exception e) {
                    System.err.println("SSL协议 " + protocol + " 初始化失败: " + e.getMessage());
                }
            }

            SSLCertificateManager.initializeSSLContext();
            System.out.println("✓ SSLCertificateManager初始化完成");

        } catch (Exception e) {
            System.err.println("✗ 强制SSL初始化失败: " + e.getMessage());
        }
    }

    @Override
    public void start(Stage primaryStage) throws IOException {
        this.primaryStage = primaryStage;
        mainStage = primaryStage;

        try {
            SSLCertificateManager.initializeSSLContext();
            System.out.println("应用启动时SSL配置初始化成功");
        } catch (Exception e) {
            System.err.println("应用启动时SSL配置初始化失败: " + e.getMessage());
        }

        String iconPath = "/com/io661/extension/img/Logo.png";
        Image mainIcon = new Image(Objects.requireNonNull(getClass().getResourceAsStream(iconPath)));
        mainStage.getIcons().add(mainIcon);

        FXMLLoader mainLoader = new FXMLLoader(getClass().getResource("/com/io661/extension/fxml/main-window.fxml"));
        Parent mainRoot = mainLoader.load();
        Scene mainScene = new Scene(mainRoot, 1366, 768);

        mainStage.setTitle("IO661增值服务");
        mainStage.setScene(mainScene);
        mainStage.setResizable(true);

        mainStage.setOnCloseRequest(event -> {
            event.consume();
            handleCloseOrMinimize();
        });

        loginStage = new Stage();
        loginStage.initOwner(mainStage);
        loginStage.initStyle(StageStyle.TRANSPARENT);
        loginStage.getIcons().add(mainIcon);

        loginStage.setOnCloseRequest(event -> {
            System.out.println("登录窗口关闭请求");
            loginStage.hide();
            if (mainRoot != null) mainRoot.setEffect(null);
        });

        FXMLLoader loginLoader = new FXMLLoader(getClass().getResource("/com/io661/extension/fxml/login.fxml"));
        Parent loginRoot = loginLoader.load();
        Scene loginScene = new Scene(loginRoot);
        loginScene.setFill(javafx.scene.paint.Color.TRANSPARENT);

        loginStage.setTitle("IO661增值服务用户登录");
        loginStage.setScene(loginScene);
        loginStage.setResizable(false);

        ColorAdjust colorAdjust = new ColorAdjust();
        colorAdjust.setBrightness(-0.6);

        mainStage.show();

        if (UserCookieManager.tokenFileExists()) {
            String savedToken = UserCookieManager.readToken();
            if (savedToken != null && !savedToken.isEmpty()) {
                try {
                    UserService userService = new UserServiceImpl();
                    LoginController.setAuthToken(savedToken);
                    boolean isValid = userService.validateToken(savedToken);
                    if (isValid) {
                        MainWindowController mainController = MainWindowController.getInstance();
                        if (mainController != null) {
                            mainController.updateAfterLogin();
                        }
                        if (mainRoot != null) mainRoot.setEffect(null);
                        return;
                    } else {
                        LoginController.setAuthToken(null);
                        UserCookieManager.deleteToken();
                    }
                } catch (Exception e) {
                    System.err.println("验证令牌时发生错误: " + e.getMessage());
                    LoginController.setAuthToken(null);
                    UserCookieManager.deleteToken();
                }
            } else {
                System.out.println("令牌文件存在但内容无效，需要登录");
                UserCookieManager.deleteToken();
            }
        } else {
            System.out.println("未找到保存的授权令牌文件，需要登录");
        }

        if (mainRoot != null) mainRoot.setEffect(colorAdjust);
        loginStage.show();

        Platform.runLater(() -> {
            if (mainStage != null && loginStage != null) {
                double centerXPosition = mainStage.getX() + mainStage.getWidth()/2 - loginStage.getWidth()/2;
                double centerYPosition = mainStage.getY() + mainStage.getHeight()/2 - loginStage.getHeight()/2;
                loginStage.setX(centerXPosition);
                loginStage.setY(centerYPosition);
                System.out.println("登录窗口位置已调整为居中");
            }
        });

        loginStage.setOnHidden(event -> {
            if (mainRoot != null) mainRoot.setEffect(null);
        });
    }

    private void handleCloseOrMinimize() {
        if (!SystemTray.isSupported()) {
            CommonShowAlert.showSyncAlert(Alert.AlertType.ERROR, "系统托盘错误", "当前系统不支持系统托盘");
            exitApplication();
            return;
        }

        Alert confirmDialog = new Alert(Alert.AlertType.CONFIRMATION);
        confirmDialog.setTitle("确认操作");
        confirmDialog.setHeaderText("选择操作");
        confirmDialog.setContentText("您希望退出应用程序还是最小化到托盘？");

        setAlertIcon(confirmDialog);
        addCustomStyle(confirmDialog);

        ButtonType exitButton = new ButtonType("退出");
        ButtonType minimizeButton = new ButtonType("最小化到托盘");
        ButtonType cancelButton = ButtonType.CANCEL;

        confirmDialog.getButtonTypes().setAll(exitButton, minimizeButton, cancelButton);

        confirmDialog.showAndWait().ifPresent(response -> {
            if (response == exitButton) {
                exitApplication();
            } else if (response == minimizeButton) {
                handleSystemTray();
            }
        });
    }

    private void setAlertIcon(Alert alert) {
        try {
            ImageView contentIcon = new ImageView(new Image(
                    Objects.requireNonNull(getClass().getResourceAsStream(DEFAULT_ICON))
            ));
            contentIcon.setFitWidth(48);
            contentIcon.setFitHeight(48);
            alert.getDialogPane().setGraphic(contentIcon);

            Stage stage = (Stage) alert.getDialogPane().getScene().getWindow();
            stage.getIcons().add(new Image(Objects.requireNonNull(getClass().getResourceAsStream(DEFAULT_ICON))));
        } catch (NullPointerException e) {
            System.err.println("警告：对话框图标加载失败: " + e.getMessage());
        }
    }

    private void addCustomStyle(Alert alert) {
        URL cssUrl = getClass().getResource(ALERT_CSS);
        if (cssUrl != null) {
            alert.getDialogPane().getStylesheets().add(cssUrl.toExternalForm());
        } else {
            System.err.println("警告：未找到Alert样式文件: " + ALERT_CSS);
        }
        alert.getDialogPane().setMinHeight(Region.USE_PREF_SIZE);
    }

    private void handleSystemTray() {
        Platform.runLater(() -> {
            if (mainStage != null) {
                mainStage.hide();
            }
        });

        SwingUtilities.invokeLater(() -> {
            try {
                if (!SystemTray.isSupported()) {
                    Platform.runLater(() ->
                            CommonShowAlert.showSyncAlert(Alert.AlertType.ERROR, "系统托盘错误", "当前系统不支持系统托盘")
                    );
                    return;
                }

                if (systemTray == null) {
                    systemTray = SystemTray.getSystemTray();
                }

                URL iconUrl = getClass().getResource("/com/io661/extension/img/io661.png");
                if (iconUrl == null) {
                    iconUrl = getClass().getResource(DEFAULT_ICON);
                    if (iconUrl == null) {
                        Platform.runLater(() ->
                                CommonShowAlert.showSyncAlert(Alert.AlertType.ERROR, "系统托盘错误", "图标资源未找到")
                        );
                        return;
                    }
                }

                java.awt.Image originalImage = Toolkit.getDefaultToolkit().getImage(iconUrl);
                java.awt.Image scaledImage = originalImage.getScaledInstance(32, 32, java.awt.Image.SCALE_SMOOTH);


                PopupMenu popupMenu = new PopupMenu();


                String osName = System.getProperty("os.name").toLowerCase();
                Font chineseFont = osName.contains("win") ?
                        new Font("Microsoft YaHei", Font.PLAIN, 12) :
                        osName.contains("mac") ? new Font("PingFang SC", Font.PLAIN, 13) :
                                new Font("WenQuanYi Micro Hei", Font.PLAIN, 12);

                popupMenu.setFont(chineseFont);

                MenuItem showItem = new MenuItem("Show Main Window");
                MenuItem exitItem = new MenuItem("Exit");
                showItem.setFont(chineseFont);
                exitItem.setFont(chineseFont);

                // 简化事件处理，避免线程问题
                showItem.addActionListener(e -> {
                    System.out.println("Tray menu: Show Main Window clicked");
                    Platform.runLater(this::showMainWindow);
                });
                exitItem.addActionListener(e -> {
                    System.out.println("Tray menu: Exit clicked");
                    Platform.runLater(this::exitApplication);
                });

                popupMenu.add(showItem);
                popupMenu.addSeparator();
                popupMenu.add(exitItem);

                if (trayIcon == null) {
                    trayIcon = new TrayIcon(scaledImage, "IO661增值服务", popupMenu);
                    trayIcon.setImageAutoSize(true);

                    // 添加ActionListener处理双击事件（这是推荐的方式）
                    trayIcon.addActionListener(e -> {
                        System.out.println("Tray icon double-clicked (ActionListener)");
                        Platform.runLater(this::showMainWindow);
                    });

                    // 同时保留MouseListener作为备选方案
                    trayIcon.addMouseListener(new MouseAdapter() {
                        @Override
                        public void mouseClicked(MouseEvent e) {
                            System.out.println("Tray icon mouse event - clicks: " + e.getClickCount() +
                                             ", button: " + e.getButton());

                            // 只处理左键双击
                            if (e.getClickCount() == 2 && e.getButton() == MouseEvent.BUTTON1) {
                                System.out.println("Left double-click detected");
                                Platform.runLater(IO661Extension.this::showMainWindow);
                            }
                        }
                    });

                    try {
                        systemTray.add(trayIcon);
                        System.out.println("托盘图标已成功添加");
                    } catch (AWTException ex) {
                        System.err.println("添加托盘图标失败: " + ex.getMessage());
                    }
                }

                trayIcon.displayMessage("应用程序已最小化", "应用程序在后台运行", TrayIcon.MessageType.INFO);
                System.out.println("托盘消息已显示");

            } catch (Exception e) {
                e.printStackTrace();
                Platform.runLater(() ->
                        CommonShowAlert.showSyncAlert(Alert.AlertType.ERROR, "系统托盘错误", e.getMessage())
                );
            }
        });
    }

    private void showMainWindow() {
        System.out.println("正在显示主窗口...");

        // 确保在JavaFX Application Thread中执行
        if (Platform.isFxApplicationThread()) {
            doShowMainWindow();
        } else {
            Platform.runLater(this::doShowMainWindow);
        }
    }

    private void doShowMainWindow() {
        try {
            if (mainStage != null) {
                System.out.println("主窗口引用存在，开始恢复窗口");

                // 如果窗口被最小化，先恢复
                if (mainStage.isIconified()) {
                    mainStage.setIconified(false);
                    System.out.println("窗口已从最小化状态恢复");
                }

                // 显示窗口
                if (!mainStage.isShowing()) {
                    mainStage.show();
                    System.out.println("窗口已显示");
                }

                // 将窗口置于前台
                mainStage.toFront();
                mainStage.requestFocus();

                // 确保窗口获得焦点（临时设置为置顶）
                mainStage.setAlwaysOnTop(true);
                Platform.runLater(() -> mainStage.setAlwaysOnTop(false));

                System.out.println("主窗口已成功显示并置前");

                // 移除托盘图标
                SwingUtilities.invokeLater(() -> {
                    if (trayIcon != null && systemTray != null) {
                        try {
                            systemTray.remove(trayIcon);
                            trayIcon = null;
                            System.out.println("托盘图标已移除");
                        } catch (Exception e) {
                            System.err.println("移除托盘图标失败: " + e.getMessage());
                        }
                    }
                });

            } else {
                System.err.println("主窗口引用为空，无法显示窗口");
            }
        } catch (Exception e) {
            System.err.println("显示主窗口时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }

    private void exitApplication() {
        System.out.println("正在关闭应用程序...");

        Platform.runLater(() -> {
            try {
                if (loginStage != null && loginStage.isShowing()) {
                    loginStage.close();
                    System.out.println("登录窗口已关闭");
                }

                SwingUtilities.invokeLater(() -> {
                    if (trayIcon != null && systemTray != null) {
                        systemTray.remove(trayIcon);
                        System.out.println("托盘图标已移除");
                        trayIcon = null;
                    }
                });

                cleanupResources();

                System.out.println("正在退出平台...");
                Platform.exit();

                // 确保完全退出
                new Thread(() -> {
                    try {
                        Thread.sleep(1000);
                        System.out.println("强制退出系统");
                        System.exit(0);
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    }
                }).start();

            } catch (Exception e) {
                System.err.println("关闭应用程序时发生错误: " + e.getMessage());
                System.exit(1);
            }
        });
    }

    private void cleanupResources() {
        try {
            System.out.println("正在清理应用程序资源...");
            System.out.println("资源清理完成");
        } catch (Exception e) {
            System.err.println("清理资源时发生错误: " + e.getMessage());
        }
    }
}