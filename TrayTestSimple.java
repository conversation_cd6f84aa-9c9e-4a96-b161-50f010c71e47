import javax.swing.*;
import java.awt.*;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;

/**
 * 简单的系统托盘测试程序
 * 用于验证托盘功能是否正常工作
 */
public class TrayTestSimple {
    
    private SystemTray systemTray;
    private TrayIcon trayIcon;
    private JFrame frame;
    
    public static void main(String[] args) {
        SwingUtilities.invokeLater(() -> {
            new TrayTestSimple().createAndShowGUI();
        });
    }
    
    private void createAndShowGUI() {
        // 创建主窗口
        frame = new JFrame("托盘测试");
        frame.setDefaultCloseOperation(JFrame.DO_NOTHING_ON_CLOSE);
        frame.setSize(300, 200);
        frame.setLocationRelativeTo(null);
        
        JButton minimizeButton = new JButton("最小化到托盘");
        minimizeButton.addActionListener(e -> minimizeToTray());
        
        frame.add(minimizeButton);
        frame.setVisible(true);
        
        // 设置关闭事件
        frame.addWindowListener(new java.awt.event.WindowAdapter() {
            @Override
            public void windowClosing(java.awt.event.WindowEvent windowEvent) {
                minimizeToTray();
            }
        });
    }
    
    private void minimizeToTray() {
        if (!SystemTray.isSupported()) {
            System.err.println("系统托盘不支持");
            return;
        }
        
        frame.setVisible(false);
        
        try {
            if (systemTray == null) {
                systemTray = SystemTray.getSystemTray();
            }
            
            if (trayIcon == null) {
                // 创建一个简单的图标
                Image image = Toolkit.getDefaultToolkit().createImage(new byte[0]);
                
                PopupMenu popupMenu = new PopupMenu();
                MenuItem showItem = new MenuItem("显示窗口");
                MenuItem exitItem = new MenuItem("退出");
                
                showItem.addActionListener(new ActionListener() {
                    @Override
                    public void actionPerformed(ActionEvent e) {
                        System.out.println("菜单项：显示窗口被点击");
                        showWindow();
                    }
                });
                
                exitItem.addActionListener(new ActionListener() {
                    @Override
                    public void actionPerformed(ActionEvent e) {
                        System.out.println("菜单项：退出被点击");
                        exitApp();
                    }
                });
                
                popupMenu.add(showItem);
                popupMenu.add(exitItem);
                
                trayIcon = new TrayIcon(image, "测试应用", popupMenu);
                trayIcon.setImageAutoSize(true);
                
                // 添加ActionListener处理双击事件
                trayIcon.addActionListener(new ActionListener() {
                    @Override
                    public void actionPerformed(ActionEvent e) {
                        System.out.println("托盘图标ActionListener被触发（双击）");
                        showWindow();
                    }
                });
                
                // 添加MouseListener作为备选方案
                trayIcon.addMouseListener(new MouseAdapter() {
                    @Override
                    public void mouseClicked(MouseEvent e) {
                        System.out.println("托盘图标鼠标事件 - 点击次数: " + e.getClickCount() + 
                                         ", 按钮: " + e.getButton());
                        if (e.getClickCount() == 2 && e.getButton() == MouseEvent.BUTTON1) {
                            System.out.println("检测到左键双击事件");
                            showWindow();
                        }
                    }
                });
                
                systemTray.add(trayIcon);
                System.out.println("托盘图标已添加");
            }
            
            trayIcon.displayMessage("应用已最小化", "双击托盘图标或右键菜单恢复", TrayIcon.MessageType.INFO);
            
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    private void showWindow() {
        System.out.println("正在显示窗口...");
        SwingUtilities.invokeLater(() -> {
            try {
                if (frame != null) {
                    frame.setVisible(true);
                    frame.toFront();
                    frame.requestFocus();
                    frame.setState(Frame.NORMAL);
                    System.out.println("窗口已显示");
                    
                    // 移除托盘图标
                    if (trayIcon != null && systemTray != null) {
                        systemTray.remove(trayIcon);
                        trayIcon = null;
                        System.out.println("托盘图标已移除");
                    }
                }
            } catch (Exception e) {
                System.err.println("显示窗口时发生错误: " + e.getMessage());
                e.printStackTrace();
            }
        });
    }
    
    private void exitApp() {
        System.out.println("正在退出应用...");
        try {
            if (trayIcon != null && systemTray != null) {
                systemTray.remove(trayIcon);
                System.out.println("托盘图标已移除");
            }
            System.exit(0);
        } catch (Exception e) {
            System.err.println("退出应用时发生错误: " + e.getMessage());
            System.exit(1);
        }
    }
}
