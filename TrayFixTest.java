import javafx.application.Application;
import javafx.application.Platform;
import javafx.scene.Scene;
import javafx.scene.control.Button;
import javafx.scene.layout.VBox;
import javafx.stage.Stage;

import javax.swing.*;
import java.awt.*;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;

/**
 * 托盘修复测试程序
 * 测试英文菜单和事件处理
 */
public class TrayFixTest extends Application {
    
    private Stage primaryStage;
    private SystemTray systemTray;
    private TrayIcon trayIcon;
    
    @Override
    public void start(Stage primaryStage) {
        this.primaryStage = primaryStage;
        
        // 创建简单的UI
        Button minimizeButton = new Button("Minimize to Tray");
        minimizeButton.setOnAction(e -> minimizeToTray());
        
        VBox root = new VBox(10);
        root.getChildren().add(minimizeButton);
        
        Scene scene = new Scene(root, 300, 200);
        primaryStage.setTitle("Tray Fix Test");
        primaryStage.setScene(scene);
        primaryStage.show();
        
        // 设置关闭事件
        primaryStage.setOnCloseRequest(event -> {
            event.consume();
            minimizeToTray();
        });
    }
    
    private void minimizeToTray() {
        if (!SystemTray.isSupported()) {
            System.err.println("System tray not supported");
            return;
        }
        
        primaryStage.hide();
        
        SwingUtilities.invokeLater(() -> {
            try {
                if (systemTray == null) {
                    systemTray = SystemTray.getSystemTray();
                }
                
                if (trayIcon == null) {
                    // 创建一个简单的图标
                    Image image = Toolkit.getDefaultToolkit().createImage(new byte[0]);
                    
                    PopupMenu popupMenu = new PopupMenu();
                    MenuItem showItem = new MenuItem("Show Window");
                    MenuItem exitItem = new MenuItem("Exit");
                    
                    // 使用英文避免乱码
                    showItem.addActionListener(e -> {
                        System.out.println("Menu: Show Window clicked");
                        Platform.runLater(this::showWindow);
                    });
                    
                    exitItem.addActionListener(e -> {
                        System.out.println("Menu: Exit clicked");
                        Platform.runLater(this::exitApp);
                    });
                    
                    popupMenu.add(showItem);
                    popupMenu.add(exitItem);
                    
                    trayIcon = new TrayIcon(image, "Test App", popupMenu);
                    trayIcon.setImageAutoSize(true);
                    
                    // 添加ActionListener处理双击事件
                    trayIcon.addActionListener(e -> {
                        System.out.println("Tray icon double-clicked (ActionListener)");
                        Platform.runLater(this::showWindow);
                    });
                    
                    // 添加MouseListener作为备选方案
                    trayIcon.addMouseListener(new MouseAdapter() {
                        @Override
                        public void mouseClicked(MouseEvent e) {
                            System.out.println("Tray mouse event - clicks: " + e.getClickCount() + 
                                             ", button: " + e.getButton());
                            if (e.getClickCount() == 2 && e.getButton() == MouseEvent.BUTTON1) {
                                System.out.println("Left double-click detected");
                                Platform.runLater(TrayFixTest.this::showWindow);
                            }
                        }
                    });
                    
                    systemTray.add(trayIcon);
                    System.out.println("Tray icon added successfully");
                }
                
                trayIcon.displayMessage("App Minimized", 
                                      "Double-click tray icon or right-click menu to restore", 
                                      TrayIcon.MessageType.INFO);
                
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
    }
    
    private void showWindow() {
        System.out.println("Showing window...");
        try {
            if (primaryStage != null) {
                primaryStage.setIconified(false);
                primaryStage.show();
                primaryStage.toFront();
                primaryStage.requestFocus();
                
                // 临时置顶确保获得焦点
                primaryStage.setAlwaysOnTop(true);
                Platform.runLater(() -> primaryStage.setAlwaysOnTop(false));
                
                System.out.println("Window shown successfully");
                
                // 移除托盘图标
                SwingUtilities.invokeLater(() -> {
                    if (trayIcon != null && systemTray != null) {
                        try {
                            systemTray.remove(trayIcon);
                            trayIcon = null;
                            System.out.println("Tray icon removed");
                        } catch (Exception e) {
                            System.err.println("Failed to remove tray icon: " + e.getMessage());
                        }
                    }
                });
            }
        } catch (Exception e) {
            System.err.println("Error showing window: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private void exitApp() {
        System.out.println("Exiting application...");
        try {
            if (trayIcon != null && systemTray != null) {
                systemTray.remove(trayIcon);
                System.out.println("Tray icon removed");
            }
            Platform.exit();
            System.exit(0);
        } catch (Exception e) {
            System.err.println("Error exiting app: " + e.getMessage());
            System.exit(1);
        }
    }
    
    public static void main(String[] args) {
        launch(args);
    }
}
