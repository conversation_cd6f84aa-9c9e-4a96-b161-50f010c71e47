package com.io661.extension;

import com.io661.extension.commonResult.CommonShowAlert;
import com.io661.extension.model.Enum.NotificationLevel;
import com.io661.extension.model.Notification.Notification;
import javafx.application.Application;
import javafx.application.Platform;
import javafx.scene.Scene;
import javafx.scene.control.Alert;
import javafx.scene.control.Button;
import javafx.scene.control.ButtonType;
import javafx.scene.image.Image;
import javafx.scene.image.ImageView;
import javafx.scene.layout.Region;
import javafx.scene.layout.VBox;
import javafx.stage.Stage;
import javax.swing.*;
import java.awt.*;
import java.net.URL;
import java.util.Objects;
import java.util.Optional;

// 主应用类
public class test extends Application {

    // 默认图标（资源路径）
    private static final String DEFAULT_ICON = "/com/io661/extension/img/Logo.png";
    // 自定义CSS路径
    private static final String ALERT_CSS = "/com/io661/extension/css/alert.css";

    private Stage primaryStage;
    private TrayIcon trayIcon;
    private SystemTray systemTray;

    @Override
    public void start(Stage primaryStage) throws Exception {
        this.primaryStage = primaryStage;
        primaryStage.setTitle("Notification Bar");

        // 设置应用图标
        try {
            primaryStage.getIcons().add(new Image(Objects.requireNonNull(getClass().getResourceAsStream(DEFAULT_ICON))));
        } catch (NullPointerException e) {
            System.err.println("警告：应用图标加载失败: " + e.getMessage());
        }

        // 创建按钮
        Button notificationButton = new Button("显示通知");
        notificationButton.setOnAction(event ->
                Notification.notify("成功", "这是一条成功的提示消息")
                        .setType(NotificationLevel.SUCCESS)
                        .show()
        );

        Button systemTrayButton = new Button("最小化到托盘");
        systemTrayButton.setOnAction(event -> handleSystemTray());

        // 设置主界面
        VBox root = new VBox(10);
        root.getChildren().addAll(notificationButton, systemTrayButton);
        Scene scene = new Scene(root, 400, 200);
        primaryStage.setScene(scene);
        primaryStage.show();

        // 注册窗口关闭事件
        primaryStage.setOnCloseRequest(event -> {
            event.consume(); // 阻止默认关闭行为
            handleCloseOrMinimize();
        });
    }

    /**
     * 选择退出还是最小化
     */
    public void handleCloseOrMinimize() {
        // 检查系统托盘支持
        if (!SystemTray.isSupported()) {
            CommonShowAlert.showSyncAlert(Alert.AlertType.ERROR, "系统托盘错误", "当前系统不支持系统托盘");
            primaryStage.close();
            return;
        }

        // 显示确认对话框
        Alert confirmDialog = new Alert(Alert.AlertType.CONFIRMATION);
        confirmDialog.setTitle("确认操作");
        confirmDialog.setHeaderText("选择操作");
        confirmDialog.setContentText("您希望退出应用程序还是最小化到托盘？");

        // 设置对话框图标
        setAlertIcon(confirmDialog);

        // 添加自定义样式
        addCustomStyle(confirmDialog);

        // 添加选项按钮
        ButtonType exitButton = new ButtonType("退出");
        ButtonType minimizeButton = new ButtonType("最小化到托盘");
        ButtonType cancelButton = ButtonType.CANCEL;

        confirmDialog.getButtonTypes().setAll(exitButton, minimizeButton, cancelButton);

        // 等待用户确认
        Optional<ButtonType> result = confirmDialog.showAndWait();
        result.ifPresent(response -> {
            if (response == exitButton) {
                // 用户确认退出
                exitApplication();
            } else if (response == minimizeButton) {
                // 用户选择最小化到托盘
                handleSystemTray();
            }
        });
    }

    /**
     * 设置对话框图标
     */
    private void setAlertIcon(Alert alert) {
        try {
            // 内容区域图标
            ImageView contentIcon = new ImageView(new Image(
                    Objects.requireNonNull(getClass().getResourceAsStream(DEFAULT_ICON))
            ));
            contentIcon.setFitWidth(48);
            contentIcon.setFitHeight(48);
            alert.getDialogPane().setGraphic(contentIcon);

            // 窗口图标
            Stage stage = (Stage) alert.getDialogPane().getScene().getWindow();
            stage.getIcons().add(new Image(Objects.requireNonNull(getClass().getResourceAsStream(DEFAULT_ICON))));
        } catch (NullPointerException e) {
            System.err.println("警告：对话框图标加载失败: " + e.getMessage());
        }
    }

    /**
     * 添加自定义样式
     */
    private void addCustomStyle(Alert alert) {
        URL cssUrl = getClass().getResource(ALERT_CSS);
        if (cssUrl != null) {
            alert.getDialogPane().getStylesheets().add(cssUrl.toExternalForm());
        } else {
            System.err.println("警告：未找到Alert样式文件: " + ALERT_CSS);
        }
        // 自适应大小
        alert.getDialogPane().setMinHeight(Region.USE_PREF_SIZE);
    }

    /**
     * 处理系统托盘
     */
    private void handleSystemTray() {
        primaryStage.hide();

        // AWT 操作必须在 EDT 线程
        SwingUtilities.invokeLater(() -> {
            try {
                // 检查系统托盘支持
                if (!SystemTray.isSupported()) {
                    Platform.runLater(() ->
                            CommonShowAlert.showSyncAlert(Alert.AlertType.ERROR, "系统托盘错误", "当前系统不支持系统托盘")
                    );
                    return;
                }

                if (systemTray == null) {
                    systemTray = SystemTray.getSystemTray();
                }

                // 加载图标
                URL iconUrl = getClass().getResource("/com/io661/extension/img/io661.png");
                if (iconUrl == null) {
                    iconUrl = getClass().getResource(DEFAULT_ICON); // 使用默认图标作为备选
                    if (iconUrl == null) {
                        Platform.runLater(() ->
                                CommonShowAlert.showSyncAlert(Alert.AlertType.ERROR, "系统托盘错误", "图标资源未找到")
                        );
                        return;
                    }
                }

                // 对图标进行缩放
                java.awt.Image originalImage = Toolkit.getDefaultToolkit().getImage(iconUrl);
                java.awt.Image scaledImage = originalImage.getScaledInstance(32, 32, java.awt.Image.SCALE_SMOOTH);

                // 创建菜单
                PopupMenu popupMenu = new PopupMenu();

                // ==== 修复中文乱码的关键修改 ==== //
                // 1. 根据操作系统选择合适的中文字体
                String osName = System.getProperty("os.name").toLowerCase();
                Font chineseFont;
                if (osName.contains("win")) {
                    chineseFont = new Font("Microsoft YaHei", Font.PLAIN, 12); // Windows
                } else if (osName.contains("mac")) {
                    chineseFont = new Font("PingFang SC", Font.PLAIN, 13); // macOS
                } else {
                    chineseFont = new Font("WenQuanYi Micro Hei", Font.PLAIN, 12); // Linux
                }

                // 2. 设置菜单字体
                popupMenu.setFont(chineseFont);

                // 3. 创建菜单项并设置字体
                MenuItem showItem = new MenuItem("显示主窗口");
                MenuItem exitItem = new MenuItem("退出应用");
                showItem.setFont(chineseFont);
                exitItem.setFont(chineseFont);
                // ================================= //

                // 绑定事件
                showItem.addActionListener(e -> Platform.runLater(this::showMainWindow));
                exitItem.addActionListener(e -> Platform.runLater(this::exitApplication));

                popupMenu.add(showItem);
                popupMenu.addSeparator();
                popupMenu.add(exitItem);

                // 创建托盘图标
                if (trayIcon == null) {
                    trayIcon = new TrayIcon(scaledImage, "IO661增值服务", popupMenu);
                    trayIcon.setImageAutoSize(true);

                    // 添加双击事件
                    trayIcon.addActionListener(e -> Platform.runLater(this::showMainWindow));

                    // 添加到系统托盘
                    systemTray.add(trayIcon);
                }

                // 显示托盘通知
                trayIcon.displayMessage("应用程序已最小化", "应用程序在后台运行", TrayIcon.MessageType.INFO);

            } catch (AWTException | NullPointerException e) {
                Platform.runLater(() ->
                        CommonShowAlert.showSyncAlert(Alert.AlertType.ERROR, "系统托盘错误", e.getMessage())
                );
            }
        });
    }

    /**
     * 显示主窗口
     */
    private void showMainWindow() {
        if (trayIcon != null && systemTray != null) {
            systemTray.remove(trayIcon);
            trayIcon = null;
        }
        primaryStage.show();
        primaryStage.setIconified(false); // 如果窗口被最小化，则恢复
        primaryStage.toFront(); // 将窗口置于前端
    }

    /**
     * 退出应用程序
     */
    private void exitApplication() {
        // 从系统托盘移除图标
        if (trayIcon != null && systemTray != null) {
            systemTray.remove(trayIcon);
        }

        // 退出应用
        Platform.exit();
        System.exit(0);
    }

    public static void main(String[] args) {
        // 确保JVM使用UTF-8编码（解决跨平台编码问题）
        System.setProperty("file.encoding", "UTF-8");
        launch(args);
    }
}