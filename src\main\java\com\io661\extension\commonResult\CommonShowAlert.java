package com.io661.extension.commonResult;

import javafx.application.Platform;
import javafx.scene.control.Alert;
import javafx.scene.image.Image;
import javafx.scene.image.ImageView;
import javafx.scene.layout.Region;
import javafx.stage.Stage;

import java.net.URL;
import java.util.Objects;
import java.util.concurrent.CountDownLatch;

public class CommonShowAlert {
    // 默认图标（资源路径）
    private static final String DEFAULT_ICON = "/com/io661/extension/img/Logo.png";
    // 自定义CSS路径
    private static final String ALERT_CSS = "/com/io661/extension/css/alert.css";

    /**
     * 异步显示提示框（JavaFX线程安全）
     */
    public static void showAlert(Alert.AlertType type, String title, String message) {
        Platform.runLater(() -> buildAlert(type, title, message).showAndWait());
    }

    /**
     * 同步显示提示框（非FX线程时阻塞等待）
     */
    public static void showSyncAlert(Alert.AlertType type, String title, String message) {
        if (Platform.isFxApplicationThread()) {
            buildAlert(type, title, message).showAndWait();
        } else {
            CountDownLatch latch = new CountDownLatch(1);
            Platform.runLater(() -> {
                buildAlert(type, title, message).showAndWait();
                latch.countDown();
            });
            try {
                latch.await();
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
    }

    /**
     * 构建并配置Alert（复用逻辑）
     */
    private static Alert buildAlert(Alert.AlertType type, String title, String message) {
        Alert alert = new Alert(type);
        alert.setTitle(title);
        alert.setHeaderText(null);
        alert.setContentText(message);

        // ========== 1. 内容区域图标（原逻辑，可保留或调整） ==========
        ImageView contentIcon = new ImageView(new Image(
                Objects.requireNonNull(CommonShowAlert.class.getResourceAsStream(DEFAULT_ICON))
        ));
        contentIcon.setFitWidth(48);  // 内容区图标尺寸
        contentIcon.setFitHeight(48);
        alert.getDialogPane().setGraphic(contentIcon);

        // ========== 2. 标题栏图标（新增逻辑） ==========
        // 获取 Alert 对应的 Stage（窗口）
        Stage stage = (Stage) alert.getDialogPane().getScene().getWindow();
        // 加载标题栏图标（可复用现有资源，或单独定义新图标）
        Image stageIcon = new Image(
                Objects.requireNonNull(CommonShowAlert.class.getResourceAsStream(DEFAULT_ICON))
        );
        // 设置窗口图标（支持多分辨率，这里只设一个）
        stage.getIcons().add(stageIcon);

        // ========== 3. 加载自定义CSS（原逻辑，保持样式美化） ==========
        URL cssUrl = CommonShowAlert.class.getResource(ALERT_CSS);
        if (cssUrl != null) {
            alert.getDialogPane().getStylesheets().add(cssUrl.toExternalForm());
        } else {
            System.err.println("警告：未找到Alert样式文件: " + ALERT_CSS);
        }

        // 自适应大小
        alert.getDialogPane().setMinHeight(Region.USE_PREF_SIZE);
        return alert;
    }
}